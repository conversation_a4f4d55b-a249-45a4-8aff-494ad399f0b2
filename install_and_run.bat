@echo off
chcp 65001 >nul
echo ================================================
echo 小红书数据采集工具 - 安装和运行脚本
echo ================================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo 依赖安装失败，尝试使用国内镜像源...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
)

echo.
echo 安装Playwright浏览器...
playwright install chromium
if errorlevel 1 (
    echo Playwright浏览器安装失败，程序可能无法正常运行
)

echo.
echo 启动程序...
python run.py

echo.
echo 程序已退出
pause
