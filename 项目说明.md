# 小红书数据采集工具 - 项目说明

## 项目概述

本项目基于您提供的 `app\xhs_search.py` 文件，开发了一个完整的GUI应用程序，实现了以下功能：

1. **搜索关键词** - 根据关键词搜索小红书笔记
2. **获取用户主页数据** - 获取指定用户的基本信息和笔记列表
3. **获取笔记详情** - 获取指定笔记的详细信息
4. **Excel导出** - 将结果自动导出为Excel文件
5. **用户数据保存** - 保存在项目同级目录
6. **浏览器自动打开和登录等待** - 集成现有的登录逻辑

## 新增文件说明

### 核心文件

1. **main_gui.py** - 主GUI应用程序
   - 提供图形化用户界面
   - 支持三种采集功能的选择
   - 集成浏览器配置和参数设置
   - 实时显示运行日志和进度

2. **config_manager.py** - 配置管理模块
   - 管理用户配置和设置
   - 自动创建用户数据目录
   - 保存浏览器状态和登录信息
   - 支持配置的持久化存储

3. **data_exporter.py** - 数据导出模块
   - 将采集结果导出为Excel格式
   - 支持多种数据结构的格式化
   - 自动调整Excel列宽
   - 生成带时间戳的文件名

### 工具文件

4. **run.py** - 开发运行脚本
   - 用于开发和测试阶段运行程序
   - 包含错误处理和依赖检查

5. **build_exe.py** - 打包脚本
   - 使用PyInstaller将程序打包成exe
   - 自动处理依赖和资源文件
   - 生成发布版本和使用说明

6. **test_gui.py** - 测试脚本
   - 验证所有组件是否正常工作
   - 测试GUI界面创建
   - 检查依赖安装情况

### 批处理文件

7. **install_and_run.bat** - 一键安装运行
   - 自动安装Python依赖
   - 安装Playwright浏览器
   - 启动GUI程序

8. **build.bat** - 一键打包
   - 自动执行打包流程
   - 生成可分发的exe文件

### 配置文件

9. **requirements.txt** - 依赖列表
   - 列出所有必需的Python包
   - 指定版本要求

10. **pyproject.toml** - 项目配置（已更新）
    - 添加了新的依赖包
    - 更新了项目描述

## 功能实现详情

### 1. GUI界面设计

- **功能选择区域**：单选按钮选择采集类型
- **参数配置区域**：动态显示不同功能的输入框
- **浏览器配置**：Chrome路径设置、无头模式选择
- **控制按钮**：开始/停止采集、打开导出目录、保存配置
- **进度显示**：进度条和实时日志
- **状态栏**：显示当前运行状态

### 2. 数据保存结构

```
项目同级目录/
└── xhs_spider_data/
    ├── config.json          # 用户配置文件
    ├── browser_data/        # 浏览器数据和登录状态
    └── exports/             # 导出的Excel文件
```

### 3. 核心功能集成

- **搜索功能**：基于原有的 `search()` 方法
- **用户数据**：基于原有的 `query_user_profile()` 方法
- **笔记详情**：基于原有的 `get_note_detail()` 方法
- **登录处理**：完全保留原有的登录逻辑
- **浏览器管理**：使用原有的浏览器启动和管理代码

## 使用流程

### 开发环境运行

1. 双击 `install_and_run.bat` 或手动安装依赖
2. 运行 `python run.py` 启动程序
3. 在GUI中配置Chrome路径和参数
4. 选择功能并输入相关参数
5. 点击"开始采集"，程序会自动打开浏览器
6. 在浏览器中完成小红书登录
7. 等待采集完成，查看导出的Excel文件

### 打包为exe

1. 双击 `build.bat` 或运行 `python build_exe.py`
2. 等待打包完成
3. 在 `release` 目录中找到生成的exe文件
4. 可以将exe文件分发给其他用户使用

## 技术特点

1. **保持原有逻辑**：完全基于现有的爬虫代码，确保稳定性
2. **异步处理**：GUI界面不会因为爬虫任务而卡死
3. **错误处理**：完善的异常处理和用户提示
4. **配置持久化**：用户设置自动保存，下次使用更方便
5. **数据结构化**：Excel导出包含完整的数据字段
6. **用户友好**：直观的GUI界面，适合非技术用户使用

## 注意事项

1. **Chrome浏览器**：需要安装Chrome浏览器并正确配置路径
2. **网络环境**：确保能正常访问小红书网站
3. **登录状态**：首次使用需要在浏览器中登录小红书
4. **采集频率**：程序内置了合理的请求间隔
5. **数据合规**：请遵守相关法律法规和网站服务条款

## 后续扩展

如需添加更多功能，可以：

1. 在 `main_gui.py` 中添加新的功能选项
2. 在 `data_exporter.py` 中添加新的导出格式
3. 在 `config_manager.py` 中添加新的配置项
4. 基于现有的 `app\xhs_search.py` 添加新的采集方法

项目结构清晰，代码模块化，便于维护和扩展。
