# -*- coding: utf-8 -*-
"""
数据导出模块
负责将爬取的数据导出为Excel格式
"""

import pandas as pd
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path
import json


class DataExporter:
    """数据导出器"""

    def __init__(self, export_dir: str):
        self.export_dir = Path(export_dir)
        self.export_dir.mkdir(exist_ok=True)

    def export_search_results(self, results: List[Dict], keyword: str) -> str:
        """
        导出搜索结果到Excel

        Args:
            results: 搜索结果列表
            keyword: 搜索关键词

        Returns:
            str: 导出文件路径
        """
        if not results:
            raise ValueError("没有数据可导出")

        # 处理数据
        processed_data = []
        for item in results:
            try:
                # 提取基本信息
                note_card = item.get('note_card', {})
                user_info = note_card.get('user', {})
                interact_info = note_card.get('interact_info', {})
                corner_tag_info = note_card.get('corner_tag_info',{})
                processed_item = {
                    '笔记ID': item.get('id', ''),
                    '笔记链接': f"https://www.xiaohongshu.com/explore/{item.get('id', '')}?xsec_token={user_info.get('xsec_token')}&xsec_source=pc_user",
                    '标题': note_card.get('display_title', ''),
                    '类型': note_card.get('type', ''),
                    '用户昵称': user_info.get('nickname', ''),
                    '用户ID': user_info.get('user_id', ''),
                    '用户主页': "https://www.xiaohongshu.com/user/profile/" + user_info.get('user_id', ''),
                    '用户头像': user_info.get('avatar', ''),
                    '点赞数': interact_info.get('liked_count', ''),
                    '收藏数': interact_info.get('collected_count', ''),
                    '评论数': interact_info.get('comment_count', ''),
                    '分享数': interact_info.get('shared_count', ''),
                    '发布时间': note_card.get('corner_tag_info', {})[0]["text"],
                }
                processed_data.append(processed_item)
            except Exception as e:
                print(f"处理数据项时出错: {e}")
                continue

        # 创建DataFrame
        df = pd.DataFrame(processed_data)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"搜索结果_{keyword}_{timestamp}.xlsx"
        filepath = self.export_dir / filename

        # 导出到Excel
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='搜索结果', index=False)

            # 调整列宽
            worksheet = writer.sheets['搜索结果']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        return str(filepath)

    def export_user_data(self, user_data: Dict, user_id: str) -> str:
        """
        导出用户数据到Excel

        Args:
            user_data: 用户数据
            user_id: 用户ID

        Returns:
            str: 导出文件路径
        """
        if not user_data:
            raise ValueError("没有用户数据可导出")

        # 处理用户基本信息
        user_info = user_data.get('basic_info', {})
        user_basic = {
            '用户ID': user_info.get('user_id', ''),
            '昵称': user_info.get('nickname', ''),
            '描述': user_info.get('desc', ''),
            '性别': user_info.get('gender', ''),
            '粉丝数': user_info.get('fans', ''),
            '关注数': user_info.get('follows', ''),
            '获赞与收藏': user_info.get('interaction', ''),
            '地区': user_info.get('ip_location', ''),
        }

        # 处理笔记列表
        notes_data = []
        notes = user_data.get('notes', [])
        for note in notes:
            try:
                note_info = {
                    '笔记ID': note.get('note_id', ''),
                    '标题': note.get('display_title', ''),
                    '类型': note.get('type', ''),
                    '点赞数': note.get('interact_info', {}).get('liked_count', ''),
                    '收藏数': note.get('interact_info', {}).get('collected_count', ''),
                    '评论数': note.get('interact_info', {}).get('comment_count', ''),
                    '发布时间': self._format_timestamp(note.get('time', 0)),
                    '链接': f"https://www.xiaohongshu.com/explore/{note.get('note_id', '')}",
                }
                notes_data.append(note_info)
            except Exception as e:
                print(f"处理笔记数据时出错: {e}")
                continue

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"用户数据_{user_id}_{timestamp}.xlsx"
        filepath = self.export_dir / filename

        # 导出到Excel
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # 用户基本信息
            user_df = pd.DataFrame([user_basic])
            user_df.to_excel(writer, sheet_name='用户信息', index=False)

            # 用户笔记
            if notes_data:
                notes_df = pd.DataFrame(notes_data)
                notes_df.to_excel(writer, sheet_name='用户笔记', index=False)

            # 调整列宽
            for sheet_name in writer.sheets:
                worksheet = writer.sheets[sheet_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

        return str(filepath)

    def export_user_works(self, user_works: List[Dict], user_id: str) -> str:
        """
        导出用户作品到Excel

        Args:
            user_works: 用户作品列表
            user_id: 用户ID

        Returns:
            str: 导出文件路径
        """
        if not user_works:
            raise ValueError("没有用户作品可导出")

        user_name = ""
        # 处理作品数据
        processed_data = []
        for work in user_works:
            try:
                user_info = work.get('user', {})
                user_name = user_info.get('nickname', '')
                processed_item = {
                    '作品ID': work.get('note_id', ''),
                    '笔记链接': f"https://www.xiaohongshu.com/explore/{work.get('note_id', '')}?xsec_token={work.get('xsec_token', '')}&xsec_source=pc_user",
                    '标题': work.get('display_title', ''),
                    '类型': work.get('type', ''),
                    '点赞数': work.get('interact_info', {}).get('liked_count', ''),
                    '用户昵称': user_info.get('nickname', ''),
                    '用户ID': user_info.get('user_id', ''),
                    '用户主页': "https://www.xiaohongshu.com/user/profile/" + user_info.get('user_id', ''),
                    '用户头像': user_info.get('avatar', ''),
                    '封面': work.get('cover', {}).get('url_pre', ''),
                    '标签': ', '.join([tag.get('name', '') for tag in work.get('tag_list', [])])
                }
                processed_data.append(processed_item)
            except Exception as e:
                print(f"处理作品数据时出错: {e}")
                continue

        # 创建DataFrame
        df = pd.DataFrame(processed_data)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"用户作品_{user_name}_{user_id}_{timestamp}.xlsx"
        filepath = self.export_dir / filename

        # 导出到Excel
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='用户作品', index=False)

            # 调整列宽
            worksheet = writer.sheets['用户作品']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        return str(filepath)

    def export_note_details(self, note_details: List[Dict], keyword: str = "详情") -> str:
        """
        导出笔记详情到Excel

        Args:
            note_details: 笔记详情列表
            keyword: 关键词或标识

        Returns:
            str: 导出文件路径
        """
        if not note_details:
            raise ValueError("没有笔记详情可导出")

        processed_data = []
        for detail in note_details:
            try:
                processed_item = {
                    '笔记ID': detail.get('note_id', ''),
                    '标题': detail.get('title', ''),
                    '描述': detail.get('desc', ''),
                    '类型': detail.get('type', ''),
                    '用户昵称': detail.get('user', {}).get('nickname', ''),
                    '用户ID': detail.get('user', {}).get('user_id', ''),
                    '点赞数': detail.get('interact_info', {}).get('liked_count', ''),
                    '收藏数': detail.get('interact_info', {}).get('collected_count', ''),
                    '评论数': detail.get('interact_info', {}).get('comment_count', ''),
                    '分享数': detail.get('interact_info', {}).get('share_count', ''),
                    '发布时间': self._format_timestamp(detail.get('time', 0)),
                    '更新时间': self._format_timestamp(detail.get('last_update_time', 0)),
                    '标签': ', '.join([tag.get('name', '') for tag in detail.get('tag_list', [])]),
                    '图片数量': len(detail.get('image_list', [])),
                    '视频链接': detail.get('video', {}).get('media', {}).get('stream', {}).get('h264', [{}])[0].get('master_url', '') if detail.get('video') else '',
                    '链接': f"https://www.xiaohongshu.com/explore/{detail.get('note_id', '')}",
                }
                processed_data.append(processed_item)
            except Exception as e:
                print(f"处理笔记详情时出错: {e}")
                continue

        # 创建DataFrame
        df = pd.DataFrame(processed_data)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"笔记详情_{keyword}_{timestamp}.xlsx"
        filepath = self.export_dir / filename

        # 导出到Excel
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='笔记详情', index=False)

            # 调整列宽
            worksheet = writer.sheets['笔记详情']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        return str(filepath)

    def _format_timestamp(self, timestamp: int) -> str:
        """格式化时间戳"""
        if not timestamp:
            return ""
        try:
            # 如果是毫秒时间戳，转换为秒
            if timestamp > 10**10:
                timestamp = timestamp / 1000
            return datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
        except:
            return str(timestamp)
