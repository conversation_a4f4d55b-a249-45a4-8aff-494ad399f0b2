# -*- coding: utf-8 -*-
"""
配置管理模块
负责管理用户配置和数据保存路径
"""

import os
import json
from typing import Dict, Any
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        # 获取项目根目录
        self.project_root = Path(__file__).parent
        # 用户数据保存在项目同级目录
        self.user_data_dir = self.project_root.parent / "xhs_spider_data"
        self.config_file = self.user_data_dir / "config.json"
        self.browser_data_dir = self.user_data_dir / "browser_data"
        self.export_dir = self.user_data_dir / "exports"
        
        # 确保目录存在
        self._ensure_directories()
        
        # 默认配置
        self.default_config = {
            "headless": False,
            "use_local_chrome": True,
            "chrome_path": self._get_default_chrome_path(),
            "search_limit": 100,
            "sort_type": "latest",
            "note_type": "all",
            "last_keywords": "",
            "last_user_ids": "",
            "export_format": "excel"
        }
        
        # 加载配置
        self.config = self._load_config()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        self.user_data_dir.mkdir(exist_ok=True)
        self.browser_data_dir.mkdir(exist_ok=True)
        self.export_dir.mkdir(exist_ok=True)
    
    def _get_default_chrome_path(self) -> str:
        """获取默认Chrome路径"""
        common_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                return path
        
        return ""
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置和用户配置
                merged_config = self.default_config.copy()
                merged_config.update(config)
                return merged_config
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return self.default_config.copy()
        else:
            return self.default_config.copy()
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, key: str, default=None):
        """获取配置值"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        self.config[key] = value
    
    def get_browser_data_dir(self) -> str:
        """获取浏览器数据目录"""
        return str(self.browser_data_dir)
    
    def get_export_dir(self) -> str:
        """获取导出目录"""
        return str(self.export_dir)
    
    def get_chrome_path(self) -> str:
        """获取Chrome路径"""
        return self.config.get("chrome_path", "")
    
    def set_chrome_path(self, path: str):
        """设置Chrome路径"""
        self.config["chrome_path"] = path
        self.save_config()
    
    def get_search_config(self) -> Dict[str, Any]:
        """获取搜索配置"""
        return {
            "headless": self.config.get("headless", False),
            "use_local_chrome": self.config.get("use_local_chrome", True),
            "chrome_path": self.config.get("chrome_path", ""),
            "user_data_dir": self.get_browser_data_dir()
        }
    
    def update_last_inputs(self, keywords: str = None, user_ids: str = None):
        """更新最后输入的内容"""
        if keywords is not None:
            self.config["last_keywords"] = keywords
        if user_ids is not None:
            self.config["last_user_ids"] = user_ids
        self.save_config()


# 全局配置管理器实例
config_manager = ConfigManager()
