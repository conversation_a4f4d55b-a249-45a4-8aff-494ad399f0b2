# -*- coding: utf-8 -*-
"""
测试新功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config_manager_new_features():
    """测试配置管理器的新功能"""
    print("测试配置管理器新功能...")
    
    try:
        from config_manager import config_manager
        
        # 测试自定义导出路径功能
        test_path = "C:\\test_export"
        config_manager.set_custom_export_path(test_path, True)
        
        assert config_manager.get_custom_export_path() == test_path
        assert config_manager.is_using_custom_export_path() == True
        
        # 测试获取导出目录（当自定义路径不存在时应该返回默认路径）
        export_dir = config_manager.get_export_dir()
        print(f"导出目录: {export_dir}")
        
        # 重置为默认
        config_manager.set_custom_export_path("", False)
        
        print("✓ 配置管理器新功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器新功能测试失败: {e}")
        return False

def test_data_exporter_new_features():
    """测试数据导出器的新功能"""
    print("\n测试数据导出器新功能...")
    
    try:
        from data_exporter import DataExporter
        from config_manager import config_manager
        
        exporter = DataExporter(config_manager.get_export_dir())
        
        # 测试用户作品导出功能（模拟数据）
        test_works = [
            {
                'note_id': 'work123',
                'display_title': '测试作品1',
                'type': 'normal',
                'interact_info': {
                    'liked_count': '100',
                    'collected_count': '50',
                    'comment_count': '20',
                    'share_count': '10'
                },
                'time': 1640995200,
                'last_update_time': 1640995200,
                'tag_list': [{'name': '测试标签'}]
            }
        ]
        
        # 检查方法是否存在
        assert hasattr(exporter, 'export_user_works')
        print("✓ export_user_works 方法存在")
        
        print("✓ 数据导出器新功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据导出器新功能测试失败: {e}")
        return False

def test_gui_new_features():
    """测试GUI新功能"""
    print("\n测试GUI新功能...")
    
    try:
        from main_gui import XHSSpiderGUI
        
        # 创建GUI实例
        app = XHSSpiderGUI()
        
        # 检查新的功能选项
        function_value = app.function_var.get()
        print(f"默认功能: {function_value}")
        
        # 检查是否有导出路径相关的变量
        assert hasattr(app, 'use_custom_export_var')
        assert hasattr(app, 'export_path_var')
        assert hasattr(app, 'update_exporter')
        
        print("✓ GUI新功能组件存在")
        
        # 测试功能切换
        app.function_var.set("user_works")
        app.on_function_change()
        print("✓ 功能切换测试通过")
        
        # 销毁窗口
        app.root.destroy()
        
        print("✓ GUI新功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ GUI新功能测试失败: {e}")
        return False

def test_function_switching():
    """测试功能切换时参数区域的变化"""
    print("\n测试功能切换...")
    
    try:
        from main_gui import XHSSpiderGUI
        
        app = XHSSpiderGUI()
        
        # 测试各种功能切换
        functions = ["search", "user", "user_works", "detail"]
        
        for func in functions:
            app.function_var.set(func)
            app.on_function_change()
            print(f"✓ 切换到 {func} 功能成功")
        
        # 销毁窗口
        app.root.destroy()
        
        print("✓ 功能切换测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 功能切换测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("新功能测试")
    print("=" * 60)
    
    tests = [
        ("配置管理器新功能", test_config_manager_new_features),
        ("数据导出器新功能", test_data_exporter_new_features),
        ("GUI新功能", test_gui_new_features),
        ("功能切换", test_function_switching)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✓ 所有新功能测试通过！")
        print("\n新功能包括:")
        print("1. ✓ 选择导出路径文件夹的选项")
        print("2. ✓ 获取用户主页作品的功能")
        print("3. ✓ 切换功能时下方选项也切换")
        print("\n可以运行程序测试完整功能:")
        print("python run.py")
    else:
        print("✗ 部分新功能测试失败！请检查错误信息。")
    print("=" * 60)

if __name__ == "__main__":
    main()
