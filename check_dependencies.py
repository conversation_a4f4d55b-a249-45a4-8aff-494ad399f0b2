# -*- coding: utf-8 -*-
"""
检查打包依赖
"""

import sys
import importlib

def check_dependency(module_name, package_name=None):
    """检查依赖是否安装"""
    if package_name is None:
        package_name = module_name
    
    try:
        importlib.import_module(module_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"✗ {package_name} 未安装")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("检查打包依赖")
    print("=" * 50)
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要3.8或更高版本")
        return False
    else:
        print("✓ Python版本符合要求")
    
    print("\n检查必需的包:")
    
    # 检查必需的包
    dependencies = [
        ("PyInstaller", "pyinstaller"),
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl"),
        ("playwright", "playwright"),
        ("httpx", "httpx"),
        ("pydantic", "pydantic"),
        ("tenacity", "tenacity")
    ]
    
    missing_packages = []
    
    for module, package in dependencies:
        if not check_dependency(module.lower(), package):
            missing_packages.append(package)
    
    print("\n" + "=" * 50)
    
    if missing_packages:
        print("❌ 缺少以下包，请先安装:")
        for package in missing_packages:
            print(f"   pip install {package}")
        print("\n或者运行:")
        print("   pip install -r requirements.txt")
        return False
    else:
        print("✅ 所有依赖都已安装，可以开始打包!")
        print("\n运行打包命令:")
        print("   python simple_build.py")
        print("或双击:")
        print("   simple_build.bat")
        return True

if __name__ == "__main__":
    success = main()
    print("\n" + "=" * 50)
    input("按回车键退出...")
    sys.exit(0 if success else 1)
