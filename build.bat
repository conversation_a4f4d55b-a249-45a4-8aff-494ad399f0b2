@echo off
chcp 65001 >nul
echo ================================================
echo 小红书数据采集工具 - 打包脚本
echo ================================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo 开始打包...
python build_exe.py

echo.
if exist "release\小红书数据采集工具.exe" (
    echo 打包成功！
    echo exe文件位于: release\小红书数据采集工具.exe
    echo.
    echo 是否打开release目录？ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        explorer release
    )
) else (
    echo 打包失败，请检查错误信息
)

echo.
pause
