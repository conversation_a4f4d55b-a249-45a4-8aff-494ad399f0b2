# -*- coding: utf-8 -*-
"""
简化的打包脚本 - 直接运行PyInstaller
"""

import sys
import subprocess
from pathlib import Path
import shutil


def build_exe():
    """构建exe文件"""
    print("=" * 50)
    print("小红书数据采集工具 - 简化构建脚本")
    print("=" * 50)
    
    # 项目根目录
    project_root = Path(__file__).parent
    
    # 确保必要的文件存在
    required_files = [
        "main_gui.py",
        "config_manager.py", 
        "data_exporter.py",
        "app/xhs_search.py",
        "app/stealth.min.js"
    ]
    
    print("检查必要文件...")
    for file in required_files:
        if not (project_root / file).exists():
            print(f"错误: 缺少必要文件 {file}")
            return False
        else:
            print(f"✓ {file}")
    
    # PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包成单个exe文件
        "--windowed",  # 不显示控制台窗口
        "--name=小红书数据采集工具",
        "--add-data=app;app",  # 添加app目录
        "--add-data=utils;utils",  # 添加utils目录
        "--hidden-import=asyncio",
        "--hidden-import=playwright",
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        "--hidden-import=app.xhs_search",
        "--hidden-import=app.client",
        "--hidden-import=app.login",
        "--hidden-import=utils.utils",
        "--hidden-import=utils.base_config",
        "--hidden-import=utils.crawler_util",
        "--hidden-import=config_manager",
        "--hidden-import=data_exporter",
        "main_gui.py"
    ]
    
    try:
        # 运行PyInstaller
        print("\n运行PyInstaller...")
        print("这可能需要几分钟时间，请耐心等待...")
        
        # 使用实时输出
        process = subprocess.Popen(
            cmd, 
            cwd=project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8',
            errors='ignore',
            universal_newlines=True
        )
        
        # 实时显示输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        return_code = process.poll()
        
        if return_code == 0:
            print("\n" + "=" * 50)
            print("PyInstaller执行成功!")
            
            # 检查生成的exe文件
            exe_path = project_root / "dist" / "小红书数据采集工具.exe"
            if exe_path.exists():
                print(f"exe文件已生成: {exe_path}")
                
                # 创建发布目录
                release_dir = project_root / "release"
                release_dir.mkdir(exist_ok=True)
                
                # 复制exe文件到发布目录
                shutil.copy2(exe_path, release_dir / "小红书数据采集工具.exe")
                
                # 创建使用说明
                create_readme(release_dir)
                
                print(f"\n发布文件已准备完成，位于: {release_dir}")
                print("=" * 50)
                return True
            else:
                print("错误: 未找到生成的exe文件")
                return False
        else:
            print(f"\nPyInstaller执行失败! 返回码: {return_code}")
            return False
            
    except FileNotFoundError:
        print("错误: 未找到PyInstaller")
        print("请先安装: pip install pyinstaller")
        return False
    except Exception as e:
        print(f"构建过程中出现错误: {e}")
        return False


def create_readme(release_dir: Path):
    """创建使用说明文件"""
    readme_content = """# 小红书数据采集工具 使用说明

## 功能介绍
本工具提供以下四种数据采集功能：
1. **搜索关键词** - 根据关键词搜索小红书笔记
2. **获取用户主页数据** - 获取指定用户的基本信息和笔记列表
3. **获取用户作品** - 专门获取用户发布的所有作品
4. **获取笔记详情** - 获取指定笔记的详细信息

## 新功能特性
- ✅ 自定义导出路径：可以选择数据保存位置
- ✅ 用户作品采集：专门获取用户发布的作品
- ✅ 动态参数切换：不同功能显示不同的参数选项

## 使用步骤

### 1. 首次使用配置
- 运行程序后，在"浏览器配置"区域设置Chrome浏览器路径
- 建议取消勾选"无头模式"，这样可以看到浏览器窗口
- 在"导出设置"中可以选择自定义导出路径
- 点击"保存配置"保存设置

### 2. 登录小红书
- 点击"开始采集"按钮
- 程序会自动打开浏览器并跳转到小红书网站
- 请在浏览器中完成登录（扫码或账号密码登录）
- 登录成功后程序会自动继续执行

### 3. 数据采集
- **搜索关键词**: 在输入框中输入关键词，多个关键词用逗号分隔
- **获取用户数据**: 输入用户ID，可在用户主页URL中找到
- **获取用户作品**: 输入用户ID，专门获取该用户的所有作品
- **获取笔记详情**: 输入笔记ID，可在笔记URL中找到

### 4. 参数设置
- 不同功能会显示不同的参数选项
- **搜索功能**: 数量限制、排序方式、笔记类型
- **用户作品**: 作品数量限制
- **导出路径**: 可选择自定义保存位置

### 5. 数据导出
- 采集完成后，数据会自动导出为Excel文件
- 点击"打开导出目录"可以查看导出的文件
- 支持自定义导出路径或使用默认路径

## 注意事项

1. **登录状态**: 程序会保存登录状态，下次使用时可能不需要重新登录
2. **采集频率**: 程序内置了合理的采集间隔，避免过于频繁的请求
3. **数据保存**: 用户数据和浏览器缓存保存在程序同级目录的"xhs_spider_data"文件夹中
4. **网络环境**: 请确保网络连接正常，能够正常访问小红书网站
5. **Chrome浏览器**: 建议使用最新版本的Chrome浏览器

## 常见问题

### Q: 程序启动后没有反应？
A: 请检查是否正确设置了Chrome浏览器路径，或者尝试重新安装Chrome浏览器。

### Q: 登录后提示失败？
A: 可能是网络问题或小红书反爬机制，请稍后重试，或者尝试手动在浏览器中完成验证。

### Q: 搜索不到结果？
A: 请检查关键词是否正确，或者尝试更换关键词。某些敏感词可能无法搜索。

### Q: 导出的Excel文件打不开？
A: 请确保安装了Excel或WPS等办公软件，或者使用在线表格工具打开。

---
小红书数据采集工具 v1.1
包含新功能：自定义导出路径、用户作品采集、动态参数切换
"""
    
    readme_file = release_dir / "使用说明.txt"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"使用说明已创建: {readme_file}")


if __name__ == "__main__":
    if build_exe():
        print("\n🎉 构建成功!")
        print("exe文件位于 release 目录中")
        input("\n按回车键退出...")
    else:
        print("\n❌ 构建失败!")
        print("请检查错误信息并重试")
        input("\n按回车键退出...")
