@echo off
chcp 65001 >nul
echo ================================================
echo 小红书数据采集工具 - 简化打包脚本
echo ================================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo PyInstaller未安装，正在安装...
    pip install pyinstaller
    if errorlevel 1 (
        echo PyInstaller安装失败
        echo 请手动运行: pip install pyinstaller
        pause
        exit /b 1
    )
)

echo PyInstaller检查通过
echo.

echo 开始打包...
python simple_build.py

echo.
if exist "release\小红书数据采集工具.exe" (
    echo 打包成功！
    echo exe文件位于: release\小红书数据采集工具.exe
    echo.
    echo 是否打开release目录？ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        explorer release
    )
) else (
    echo 打包失败，请检查错误信息
)

echo.
pause
