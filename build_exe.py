# -*- coding: utf-8 -*-
"""
打包脚本 - 将应用程序打包成exe文件
"""

import os
import sys
import shutil
from pathlib import Path
import subprocess


def build_exe():
    """构建exe文件"""
    print("开始构建exe文件...")

    # 项目根目录
    project_root = Path(__file__).parent

    # 确保必要的文件存在
    required_files = [
        "main_gui.py",
        "config_manager.py",
        "data_exporter.py",
        "app/xhs_search.py",
        "app/stealth.min.js"
    ]

    for file in required_files:
        if not (project_root / file).exists():
            print(f"错误: 缺少必要文件 {file}")
            return False

    # PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包成单个exe文件
        "--windowed",  # 不显示控制台窗口
        "--name=小红书数据采集工具",
        "--icon=icon.ico" if (project_root / "icon.ico").exists() else "",
        "--add-data=app;app",  # 添加app目录
        "--add-data=utils;utils",  # 添加utils目录
        "--add-data=app/stealth.min.js;.",  # 添加stealth.min.js文件到根目录
        "--hidden-import=asyncio",
        "--hidden-import=playwright",
        "--hidden-import=pandas",
        "--hidden-import=openpyxl",
        "--hidden-import=tkinter",
        "--hidden-import=tkinter.ttk",
        "--hidden-import=tkinter.messagebox",
        "--hidden-import=tkinter.filedialog",
        "--hidden-import=tkinter.scrolledtext",
        "--hidden-import=app",
        "--hidden-import=app.xhs_search",
        "--hidden-import=app.client",
        "--hidden-import=app.login",
        "--hidden-import=utils.utils",
        "--hidden-import=utils.base_config",
        "--hidden-import=utils.crawler_util",
        "main_gui.py"
    ]

    # 过滤空字符串
    cmd = [arg for arg in cmd if arg]

    try:
        # 运行PyInstaller
        print("运行PyInstaller...")
        print(f"命令: {' '.join(cmd)}")

        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True, encoding='utf-8', errors='ignore')

        if result.returncode == 0:
            print("PyInstaller执行成功!")
            print("输出:", result.stdout)

            # 检查生成的exe文件
            exe_path = project_root / "dist" / "小红书数据采集工具.exe"
            if exe_path.exists():
                print(f"exe文件已生成: {exe_path}")

                # 创建发布目录
                release_dir = project_root / "release"
                release_dir.mkdir(exist_ok=True)

                # 复制exe文件到发布目录
                shutil.copy2(exe_path, release_dir / "小红书数据采集工具.exe")

                # 创建使用说明
                create_readme(release_dir)

                print(f"发布文件已准备完成，位于: {release_dir}")
                return True
            else:
                print("错误: 未找到生成的exe文件")
                return False
        else:
            print("PyInstaller执行失败!")
            print("错误输出:", result.stderr)
            return False

    except FileNotFoundError:
        print("错误: 未找到PyInstaller，请先安装: pip install pyinstaller")
        return False
    except Exception as e:
        print(f"构建过程中出现错误: {e}")
        return False


def create_readme(release_dir: Path):
    """创建使用说明文件"""
    readme_content = """# 小红书数据采集工具 使用说明

## 功能介绍
本工具提供以下三种数据采集功能：
1. **搜索关键词** - 根据关键词搜索小红书笔记
2. **获取用户主页数据** - 获取指定用户的基本信息和笔记列表
3. **获取笔记详情** - 获取指定笔记的详细信息

## 使用步骤

### 1. 首次使用配置
- 运行程序后，在"浏览器配置"区域设置Chrome浏览器路径
- 建议取消勾选"无头模式"，这样可以看到浏览器窗口
- 点击"保存配置"保存设置

### 2. 登录小红书
- 点击"开始采集"按钮
- 程序会自动打开浏览器并跳转到小红书网站
- 请在浏览器中完成登录（扫码或账号密码登录）
- 登录成功后程序会自动继续执行

### 3. 数据采集
- **搜索关键词**: 在输入框中输入关键词，多个关键词用逗号分隔
- **获取用户数据**: 输入用户ID，可在用户主页URL中找到
- **获取笔记详情**: 输入笔记ID，可在笔记URL中找到

### 4. 参数设置
- **数量限制**: 设置每个关键词或用户最多获取的数据条数
- **排序方式**: 选择搜索结果的排序方式（最新、热门、综合）
- **笔记类型**: 选择要获取的笔记类型（全部、视频、图片）

### 5. 数据导出
- 采集完成后，数据会自动导出为Excel文件
- 点击"打开导出目录"可以查看导出的文件
- 导出文件保存在程序同级目录的"xhs_spider_data/exports"文件夹中

## 注意事项

1. **登录状态**: 程序会保存登录状态，下次使用时可能不需要重新登录
2. **采集频率**: 程序内置了合理的采集间隔，避免过于频繁的请求
3. **数据保存**: 用户数据和浏览器缓存保存在程序同级目录的"xhs_spider_data"文件夹中
4. **网络环境**: 请确保网络连接正常，能够正常访问小红书网站
5. **Chrome浏览器**: 建议使用最新版本的Chrome浏览器

## 常见问题

### Q: 程序启动后没有反应？
A: 请检查是否正确设置了Chrome浏览器路径，或者尝试重新安装Chrome浏览器。

### Q: 登录后提示失败？
A: 可能是网络问题或小红书反爬机制，请稍后重试，或者尝试手动在浏览器中完成验证。

### Q: 搜索不到结果？
A: 请检查关键词是否正确，或者尝试更换关键词。某些敏感词可能无法搜索。

### Q: 导出的Excel文件打不开？
A: 请确保安装了Excel或WPS等办公软件，或者使用在线表格工具打开。

## 技术支持
如有问题，请检查程序运行日志中的错误信息，或联系技术支持。

---
小红书数据采集工具 v1.0
"""

    readme_file = release_dir / "使用说明.txt"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)

    print(f"使用说明已创建: {readme_file}")


def install_dependencies():
    """安装依赖"""
    print("检查并安装依赖...")

    dependencies = [
        "pyinstaller>=6.0.0",
        "pandas>=2.0.0",
        "openpyxl>=3.1.0"
    ]

    for dep in dependencies:
        try:
            print(f"安装 {dep}...")
            # 尝试使用pip模块
            result = subprocess.run([sys.executable, "-m", "pip", "install", dep],
                                  capture_output=True, text=True, encoding='utf-8', errors='ignore')
            if result.returncode != 0:
                print(f"警告: {dep} 安装可能失败，但继续构建...")
                print(f"错误信息: {result.stderr}")
        except Exception as e:
            print(f"警告: 安装 {dep} 时出错: {e}，但继续构建...")

    print("依赖检查完成!")
    return True


def main():
    """主函数"""
    print("=" * 50)
    print("小红书数据采集工具 - 构建脚本")
    print("=" * 50)

    # 检查Python版本
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        return

    # 安装依赖
    if not install_dependencies():
        print("依赖安装失败，构建终止")
        return

    # 构建exe
    if build_exe():
        print("\n" + "=" * 50)
        print("构建成功!")
        print("exe文件位于 release 目录中")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("构建失败!")
        print("请检查错误信息并重试")
        print("=" * 50)


if __name__ == "__main__":
    main()
