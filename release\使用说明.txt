# 小红书数据采集工具 使用说明

## 功能介绍
本工具提供以下四种数据采集功能：
1. **搜索关键词** - 根据关键词搜索小红书笔记
2. **获取用户主页数据** - 获取指定用户的基本信息和笔记列表
3. **获取用户作品** - 专门获取用户发布的所有作品
4. **获取笔记详情** - 获取指定笔记的详细信息

## 新功能特性
- ✅ 自定义导出路径：可以选择数据保存位置
- ✅ 用户作品采集：专门获取用户发布的作品
- ✅ 动态参数切换：不同功能显示不同的参数选项

## 使用步骤

### 1. 首次使用配置
- 运行程序后，在"浏览器配置"区域设置Chrome浏览器路径
- 建议取消勾选"无头模式"，这样可以看到浏览器窗口
- 在"导出设置"中可以选择自定义导出路径
- 点击"保存配置"保存设置

### 2. 登录小红书
- 点击"开始采集"按钮
- 程序会自动打开浏览器并跳转到小红书网站
- 请在浏览器中完成登录（扫码或账号密码登录）
- 登录成功后程序会自动继续执行

### 3. 数据采集
- **搜索关键词**: 在输入框中输入关键词，多个关键词用逗号分隔
- **获取用户数据**: 输入用户ID，可在用户主页URL中找到
- **获取用户作品**: 输入用户ID，专门获取该用户的所有作品
- **获取笔记详情**: 输入笔记ID，可在笔记URL中找到

### 4. 参数设置
- 不同功能会显示不同的参数选项
- **搜索功能**: 数量限制、排序方式、笔记类型
- **用户作品**: 作品数量限制
- **导出路径**: 可选择自定义保存位置

### 5. 数据导出
- 采集完成后，数据会自动导出为Excel文件
- 点击"打开导出目录"可以查看导出的文件
- 支持自定义导出路径或使用默认路径

## 注意事项

1. **登录状态**: 程序会保存登录状态，下次使用时可能不需要重新登录
2. **采集频率**: 程序内置了合理的采集间隔，避免过于频繁的请求
3. **数据保存**: 用户数据和浏览器缓存保存在程序同级目录的"xhs_spider_data"文件夹中
4. **网络环境**: 请确保网络连接正常，能够正常访问小红书网站
5. **Chrome浏览器**: 建议使用最新版本的Chrome浏览器

## 常见问题

### Q: 程序启动后没有反应？
A: 请检查是否正确设置了Chrome浏览器路径，或者尝试重新安装Chrome浏览器。

### Q: 登录后提示失败？
A: 可能是网络问题或小红书反爬机制，请稍后重试，或者尝试手动在浏览器中完成验证。

### Q: 搜索不到结果？
A: 请检查关键词是否正确，或者尝试更换关键词。某些敏感词可能无法搜索。

### Q: 导出的Excel文件打不开？
A: 请确保安装了Excel或WPS等办公软件，或者使用在线表格工具打开。

---
小红书数据采集工具 v1.1
包含新功能：自定义导出路径、用户作品采集、动态参数切换
