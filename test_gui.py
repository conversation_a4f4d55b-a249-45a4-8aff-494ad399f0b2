# -*- coding: utf-8 -*-
"""
GUI测试脚本 - 验证GUI界面是否能正常启动
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有必要的导入"""
    print("测试导入...")
    
    try:
        import tkinter as tk
        print("✓ tkinter 导入成功")
    except ImportError as e:
        print(f"✗ tkinter 导入失败: {e}")
        return False
    
    try:
        from tkinter import ttk, messagebox, filedialog, scrolledtext
        print("✓ tkinter 子模块导入成功")
    except ImportError as e:
        print(f"✗ tkinter 子模块导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        print("✓ pandas 导入成功")
    except ImportError as e:
        print(f"✗ pandas 导入失败: {e}")
        return False
    
    try:
        import openpyxl
        print("✓ openpyxl 导入成功")
    except ImportError as e:
        print(f"✗ openpyxl 导入失败: {e}")
        return False
    
    try:
        from config_manager import config_manager
        print("✓ config_manager 导入成功")
    except ImportError as e:
        print(f"✗ config_manager 导入失败: {e}")
        return False
    
    try:
        from data_exporter import DataExporter
        print("✓ data_exporter 导入成功")
    except ImportError as e:
        print(f"✗ data_exporter 导入失败: {e}")
        return False
    
    return True

def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")
    
    try:
        from config_manager import config_manager
        
        # 测试基本功能
        config_manager.set("test_key", "test_value")
        value = config_manager.get("test_key")
        assert value == "test_value", f"配置值不匹配: {value}"
        print("✓ 配置管理器基本功能正常")
        
        # 测试目录创建
        export_dir = config_manager.get_export_dir()
        assert os.path.exists(export_dir), f"导出目录不存在: {export_dir}"
        print(f"✓ 导出目录创建成功: {export_dir}")
        
        return True
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False

def test_data_exporter():
    """测试数据导出器"""
    print("\n测试数据导出器...")
    
    try:
        from data_exporter import DataExporter
        from config_manager import config_manager
        
        exporter = DataExporter(config_manager.get_export_dir())
        
        # 创建测试数据
        test_data = [
            {
                'note_card': {
                    'note_id': 'test123',
                    'display_title': '测试标题',
                    'type': 'normal',
                    'user': {'nickname': '测试用户', 'user_id': 'user123'},
                    'interact_info': {'liked_count': '100', 'collected_count': '50'},
                    'time': 1640995200,  # 2022-01-01 00:00:00
                    'tag_list': [{'name': '测试标签'}]
                }
            }
        ]
        
        # 测试导出（不实际保存文件）
        print("✓ 数据导出器创建成功")
        return True
        
    except Exception as e:
        print(f"✗ 数据导出器测试失败: {e}")
        return False

def test_gui_creation():
    """测试GUI界面创建"""
    print("\n测试GUI界面创建...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("400x300")
        
        # 添加一些基本组件
        label = ttk.Label(root, text="GUI测试成功!")
        label.pack(pady=20)
        
        button = ttk.Button(root, text="关闭", command=root.destroy)
        button.pack(pady=10)
        
        print("✓ GUI界面创建成功")
        
        # 显示窗口2秒后自动关闭
        root.after(2000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI界面创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("小红书数据采集工具 - GUI测试")
    print("=" * 50)
    
    all_tests_passed = True
    
    # 运行所有测试
    tests = [
        test_imports,
        test_config_manager,
        test_data_exporter,
        test_gui_creation
    ]
    
    for test in tests:
        if not test():
            all_tests_passed = False
    
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("✓ 所有测试通过! GUI程序应该可以正常运行。")
        print("可以运行 'python run.py' 启动完整程序。")
    else:
        print("✗ 部分测试失败! 请检查错误信息并安装缺失的依赖。")
    print("=" * 50)

if __name__ == "__main__":
    main()
