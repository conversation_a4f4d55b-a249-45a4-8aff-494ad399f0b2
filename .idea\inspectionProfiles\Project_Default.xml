<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DuplicatedCode" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <Languages>
        <language minSize="105" name="Python" />
      </Languages>
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="6">
            <item index="0" class="java.lang.String" itemvalue="asyncio" />
            <item index="1" class="java.lang.String" itemvalue="pytest" />
            <item index="2" class="java.lang.String" itemvalue="pytest-cov" />
            <item index="3" class="java.lang.String" itemvalue="mypy" />
            <item index="4" class="java.lang.String" itemvalue="black" />
            <item index="5" class="java.lang.String" itemvalue="flake8" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N806" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>