# -*- coding: utf-8 -*-
"""
测试修复是否有效
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_stealth_import():
    """测试stealth.min.js导入是否正常"""
    print("测试stealth.min.js路径修复...")
    
    try:
        from app.xhs_search import XiaoHongShuSearch
        
        # 创建实例（不初始化）
        searcher = XiaoHongShuSearch(headless=True)
        
        # 检查stealth.min.js路径
        stealth_path = os.path.join(os.path.dirname(searcher.__class__.__module__.replace('.', os.sep)), "stealth.min.js")
        
        # 实际使用的路径
        import app.xhs_search
        actual_path = os.path.join(os.path.dirname(app.xhs_search.__file__), "stealth.min.js")
        
        print(f"stealth.min.js路径: {actual_path}")
        print(f"文件是否存在: {os.path.exists(actual_path)}")
        
        if os.path.exists(actual_path):
            print("✓ stealth.min.js路径修复成功")
            return True
        else:
            print("✗ stealth.min.js文件仍然找不到")
            return False
            
    except Exception as e:
        print(f"✗ 测试stealth.min.js导入时出错: {e}")
        return False

def test_gui_startup():
    """测试GUI是否能正常启动"""
    print("\n测试GUI启动...")
    
    try:
        from main_gui import XHSSpiderGUI
        
        # 创建GUI实例
        app = XHSSpiderGUI()
        print("✓ GUI实例创建成功")
        
        # 测试配置加载
        from config_manager import config_manager
        config = config_manager.get_search_config()
        print(f"✓ 配置加载成功: {config}")
        
        # 销毁窗口
        app.root.destroy()
        
        print("✓ GUI启动测试通过")
        return True
        
    except Exception as e:
        print(f"✗ GUI启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理是否正常"""
    print("\n测试错误处理...")
    
    try:
        from main_gui import XHSSpiderGUI
        
        app = XHSSpiderGUI()
        
        # 测试错误消息显示方法
        test_message = "这是一个测试错误消息"
        app.show_error_message(test_message)
        print("✓ 错误消息显示方法正常")
        
        # 销毁窗口
        app.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("修复验证测试")
    print("=" * 60)
    
    tests = [
        ("stealth.min.js路径修复", test_stealth_import),
        ("GUI启动测试", test_gui_startup),
        ("错误处理测试", test_error_handling)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✓ 所有修复验证测试通过！")
        print("程序应该可以正常运行了。")
        print("\n可以运行以下命令启动程序:")
        print("python run.py")
    else:
        print("✗ 部分测试失败！请检查错误信息。")
    print("=" * 60)

if __name__ == "__main__":
    main()
