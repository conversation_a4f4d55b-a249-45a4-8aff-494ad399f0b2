<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="634ff4b8-f842-4d61-896d-042319e2ece4" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.python-version" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/__init__.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/client.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/exception.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/field.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/help.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/login.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/m_xiaohongshu.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/stealth.min.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/app/xhs_search.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/hello.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/pyproject.toml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/utils/base_config.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/utils/crawler_util.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/utils/utils.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/uv.lock" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2xlnv2iwygEhR6DAnA8cK4Kd5G7" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "F:/py/xhs-spider/utils",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="F:\py\xhs-spider\utils" />
      <recent name="F:\py\xhs-spider" />
      <recent name="F:\py\xhs-spider\app" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="F:\py\xhs-spider\app" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="xhs_search" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="xhs-spider" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/app" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/app/xhs_search.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.xhs_search" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="634ff4b8-f842-4d61-896d-042319e2ece4" name="Changes" comment="" />
      <created>1748521754698</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748521754698</updated>
      <workItem from="1748521755713" duration="2842000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/xhs_spider$xhs_search.coverage" NAME="xhs_search Coverage Results" MODIFIED="1748523882097" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/app" />
  </component>
</project>