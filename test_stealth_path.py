# -*- coding: utf-8 -*-
"""
测试stealth.min.js路径是否正确
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_stealth_path():
    """测试stealth.min.js文件路径"""
    print("测试stealth.min.js文件路径...")
    
    # 测试相对路径（原始方式）
    relative_path = "stealth.min.js"
    print(f"相对路径: {relative_path}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"相对路径是否存在: {os.path.exists(relative_path)}")
    
    # 测试绝对路径（修复后的方式）
    app_dir = os.path.join(project_root, "app")
    absolute_path = os.path.join(app_dir, "stealth.min.js")
    print(f"绝对路径: {absolute_path}")
    print(f"绝对路径是否存在: {os.path.exists(absolute_path)}")
    
    # 测试从app模块内部获取路径
    try:
        import app.xhs_search
        app_module_dir = os.path.dirname(app.xhs_search.__file__)
        module_path = os.path.join(app_module_dir, "stealth.min.js")
        print(f"模块路径: {module_path}")
        print(f"模块路径是否存在: {os.path.exists(module_path)}")
        
        if os.path.exists(module_path):
            print("✓ stealth.min.js文件路径修复成功")
            return True
        else:
            print("✗ stealth.min.js文件仍然找不到")
            return False
            
    except Exception as e:
        print(f"✗ 测试模块路径时出错: {e}")
        return False

def test_file_content():
    """测试stealth.min.js文件内容"""
    print("\n测试stealth.min.js文件内容...")
    
    try:
        app_dir = os.path.join(project_root, "app")
        stealth_path = os.path.join(app_dir, "stealth.min.js")
        
        if os.path.exists(stealth_path):
            with open(stealth_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"文件大小: {len(content)} 字符")
                print(f"文件开头: {content[:100]}...")
                
                if len(content) > 0:
                    print("✓ stealth.min.js文件内容正常")
                    return True
                else:
                    print("✗ stealth.min.js文件为空")
                    return False
        else:
            print("✗ stealth.min.js文件不存在")
            return False
            
    except Exception as e:
        print(f"✗ 读取stealth.min.js文件时出错: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("stealth.min.js 路径修复测试")
    print("=" * 60)
    
    path_test = test_stealth_path()
    content_test = test_file_content()
    
    print("\n" + "=" * 60)
    if path_test and content_test:
        print("✓ 所有测试通过！stealth.min.js路径问题已修复。")
        print("现在可以正常运行GUI程序了。")
    else:
        print("✗ 部分测试失败！请检查stealth.min.js文件。")
    print("=" * 60)

if __name__ == "__main__":
    main()
