# -*- coding: utf-8 -*-
"""
小红书数据采集工具 - 主GUI界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import asyncio
import threading
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from app.xhs_search import XiaoHongShuSearch
from app.field import SearchSortType, SearchNoteType
from config_manager import config_manager
from data_exporter import DataExporter


class XHSSpiderGUI:
    """小红书爬虫GUI主界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("小红书数据采集工具 v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 设置图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # 初始化变量
        self.searcher = None
        self.is_running = False
        self.current_task = None

        # 创建数据导出器
        self.exporter = DataExporter(config_manager.get_export_dir())

        # 创建界面
        self.create_widgets()

        # 加载配置
        self.load_config()

    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="小红书数据采集工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 功能选择区域
        function_frame = ttk.LabelFrame(main_frame, text="功能选择", padding="10")
        function_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        function_frame.columnconfigure(1, weight=1)

        self.function_var = tk.StringVar(value="search")

        ttk.Radiobutton(function_frame, text="搜索关键词", variable=self.function_var,
                       value="search", command=self.on_function_change).grid(row=0, column=0, sticky=tk.W, padx=(0, 15))
        ttk.Radiobutton(function_frame, text="获取用户主页数据", variable=self.function_var,
                       value="user", command=self.on_function_change).grid(row=0, column=1, sticky=tk.W, padx=(0, 15))
        ttk.Radiobutton(function_frame, text="获取用户作品", variable=self.function_var,
                       value="user_works", command=self.on_function_change).grid(row=0, column=2, sticky=tk.W, padx=(0, 15))
        ttk.Radiobutton(function_frame, text="获取笔记详情", variable=self.function_var,
                       value="detail", command=self.on_function_change).grid(row=0, column=3, sticky=tk.W)

        # 参数配置区域
        config_frame = ttk.LabelFrame(main_frame, text="参数配置", padding="10")
        config_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        # 输入区域（动态变化）
        self.input_frame = ttk.Frame(config_frame)
        self.input_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        self.input_frame.columnconfigure(1, weight=1)

        # 动态参数区域（根据功能变化）
        self.dynamic_params_frame = ttk.Frame(config_frame)
        self.dynamic_params_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        self.dynamic_params_frame.columnconfigure(1, weight=1)

        # 导出设置
        export_frame = ttk.LabelFrame(config_frame, text="导出设置", padding="5")
        export_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        export_frame.columnconfigure(1, weight=1)

        # 使用自定义导出路径
        self.use_custom_export_var = tk.BooleanVar(value=config_manager.is_using_custom_export_path())
        ttk.Checkbutton(export_frame, text="使用自定义导出路径", variable=self.use_custom_export_var,
                       command=self.on_export_path_change).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=2)

        # 导出路径
        ttk.Label(export_frame, text="导出路径:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.export_path_var = tk.StringVar(value=config_manager.get_custom_export_path())
        self.export_path_entry = ttk.Entry(export_frame, textvariable=self.export_path_var, state="readonly" if not self.use_custom_export_var.get() else "normal")
        self.export_path_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        self.export_browse_button = ttk.Button(export_frame, text="浏览", command=self.browse_export_path,
                                             state="normal" if self.use_custom_export_var.get() else "disabled")
        self.export_browse_button.grid(row=1, column=2, pady=2)

        # 浏览器配置
        browser_frame = ttk.LabelFrame(main_frame, text="浏览器配置", padding="10")
        browser_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        browser_frame.columnconfigure(1, weight=1)

        # 无头模式
        self.headless_var = tk.BooleanVar(value=config_manager.get("headless", False))
        ttk.Checkbutton(browser_frame, text="无头模式（后台运行）", variable=self.headless_var).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=2)

        # 使用本地Chrome
        self.use_local_chrome_var = tk.BooleanVar(value=config_manager.get("use_local_chrome", True))
        ttk.Checkbutton(browser_frame, text="使用本地Chrome浏览器", variable=self.use_local_chrome_var).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=2)

        # Chrome路径
        ttk.Label(browser_frame, text="Chrome路径:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.chrome_path_var = tk.StringVar(value=config_manager.get_chrome_path())
        chrome_path_entry = ttk.Entry(browser_frame, textvariable=self.chrome_path_var)
        chrome_path_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        ttk.Button(browser_frame, text="浏览", command=self.browse_chrome_path).grid(row=2, column=2, pady=2)

        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=4, column=0, columnspan=3, pady=(0, 10))

        self.start_button = ttk.Button(control_frame, text="开始采集", command=self.start_collection)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(control_frame, text="停止采集", command=self.stop_collection, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="打开导出目录", command=self.open_export_dir).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT)

        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E))

        # 初始化输入区域
        self.on_function_change()

    def create_input_widgets(self):
        """根据选择的功能创建对应的输入组件"""
        # 清除现有组件
        for widget in self.input_frame.winfo_children():
            widget.destroy()
        for widget in self.dynamic_params_frame.winfo_children():
            widget.destroy()

        function = self.function_var.get()

        if function == "search":
            # 搜索关键词输入
            ttk.Label(self.input_frame, text="搜索关键词:").grid(row=0, column=0, sticky=tk.W, pady=2)
            self.keywords_var = tk.StringVar(value=config_manager.get("last_keywords", ""))
            keywords_entry = ttk.Entry(self.input_frame, textvariable=self.keywords_var)
            keywords_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
            ttk.Label(self.input_frame, text="(多个关键词用逗号分隔)", font=("Arial", 8)).grid(row=1, column=1, sticky=tk.W, padx=(5, 0))

            # 搜索相关参数
            ttk.Label(self.dynamic_params_frame, text="数量限制:").grid(row=0, column=0, sticky=tk.W, pady=2)
            self.limit_var = tk.StringVar(value=str(config_manager.get("search_limit", 100)))
            limit_entry = ttk.Entry(self.dynamic_params_frame, textvariable=self.limit_var, width=10)
            limit_entry.grid(row=0, column=1, sticky=tk.W, pady=2)

            ttk.Label(self.dynamic_params_frame, text="排序方式:").grid(row=1, column=0, sticky=tk.W, pady=2)
            self.sort_var = tk.StringVar(value=config_manager.get("sort_type", "latest"))
            sort_combo = ttk.Combobox(self.dynamic_params_frame, textvariable=self.sort_var, width=15, state="readonly")
            sort_combo['values'] = ("latest", "popularity", "general")
            sort_combo.grid(row=1, column=1, sticky=tk.W, pady=2)

            ttk.Label(self.dynamic_params_frame, text="笔记类型:").grid(row=2, column=0, sticky=tk.W, pady=2)
            self.note_type_var = tk.StringVar(value=config_manager.get("note_type", "all"))
            note_type_combo = ttk.Combobox(self.dynamic_params_frame, textvariable=self.note_type_var, width=15, state="readonly")
            note_type_combo['values'] = ("all", "video", "image")
            note_type_combo.grid(row=2, column=1, sticky=tk.W, pady=2)

        elif function == "user":
            # 用户ID输入
            ttk.Label(self.input_frame, text="用户ID:").grid(row=0, column=0, sticky=tk.W, pady=2)
            self.user_ids_var = tk.StringVar(value=config_manager.get("last_user_ids", ""))
            user_ids_entry = ttk.Entry(self.input_frame, textvariable=self.user_ids_var)
            user_ids_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
            ttk.Label(self.input_frame, text="(多个用户ID用逗号分隔)", font=("Arial", 8)).grid(row=1, column=1, sticky=tk.W, padx=(5, 0))

            # 用户数据相关参数
            ttk.Label(self.dynamic_params_frame, text="获取用户基本信息和笔记列表", font=("Arial", 9)).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=2)

        elif function == "user_works":
            # 用户ID输入（获取作品）
            ttk.Label(self.input_frame, text="用户ID:").grid(row=0, column=0, sticky=tk.W, pady=2)
            self.user_works_ids_var = tk.StringVar(value=config_manager.get("last_user_ids", ""))
            user_works_ids_entry = ttk.Entry(self.input_frame, textvariable=self.user_works_ids_var)
            user_works_ids_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
            ttk.Label(self.input_frame, text="(多个用户ID用逗号分隔)", font=("Arial", 8)).grid(row=1, column=1, sticky=tk.W, padx=(5, 0))

            # 用户作品相关参数
            ttk.Label(self.dynamic_params_frame, text="作品数量限制:").grid(row=0, column=0, sticky=tk.W, pady=2)
            self.works_limit_var = tk.StringVar(value=str(config_manager.get("search_limit", 100)))
            works_limit_entry = ttk.Entry(self.dynamic_params_frame, textvariable=self.works_limit_var, width=10)
            works_limit_entry.grid(row=0, column=1, sticky=tk.W, pady=2)

            ttk.Label(self.dynamic_params_frame, text="获取用户发布的所有作品", font=("Arial", 9)).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=2)

        elif function == "detail":
            # 笔记ID输入
            ttk.Label(self.input_frame, text="笔记ID:").grid(row=0, column=0, sticky=tk.W, pady=2)
            self.note_ids_var = tk.StringVar(value="")
            note_ids_entry = ttk.Entry(self.input_frame, textvariable=self.note_ids_var)
            note_ids_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
            ttk.Label(self.input_frame, text="(多个笔记ID用逗号分隔)", font=("Arial", 8)).grid(row=1, column=1, sticky=tk.W, padx=(5, 0))

            # 笔记详情相关参数
            ttk.Label(self.dynamic_params_frame, text="获取笔记的详细信息", font=("Arial", 9)).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=2)

    def on_function_change(self):
        """功能选择改变时的回调"""
        self.create_input_widgets()

    def browse_chrome_path(self):
        """浏览Chrome路径"""
        filename = filedialog.askopenfilename(
            title="选择Chrome浏览器",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if filename:
            self.chrome_path_var.set(filename)

    def browse_export_path(self):
        """浏览导出路径"""
        directory = filedialog.askdirectory(
            title="选择导出目录",
            initialdir=self.export_path_var.get() or config_manager.get_export_dir()
        )
        if directory:
            self.export_path_var.set(directory)
            config_manager.set_custom_export_path(directory, self.use_custom_export_var.get())

    def on_export_path_change(self):
        """导出路径选项改变时的回调"""
        use_custom = self.use_custom_export_var.get()

        # 更新控件状态
        if use_custom:
            self.export_path_entry.config(state="normal")
            self.export_browse_button.config(state="normal")
        else:
            self.export_path_entry.config(state="readonly")
            self.export_browse_button.config(state="disabled")
            # 显示默认路径
            self.export_path_var.set(config_manager.get_export_dir())

        # 保存配置
        config_manager.set_custom_export_path(
            self.export_path_var.get(),
            use_custom
        )

    def log_message(self, message: str):
        """添加日志消息"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update_idletasks()

    def update_status(self, status: str):
        """更新状态栏"""
        self.status_var.set(status)
        self.root.update_idletasks()

    def show_error_message(self, message: str):
        """显示错误消息"""
        try:
            messagebox.showerror("错误", message)
        except Exception as e:
            print(f"显示错误消息失败: {e}")
            print(f"原始错误: {message}")

    def load_config(self):
        """加载配置"""
        # 配置已在初始化时加载，这里可以做额外的UI更新
        pass

    def save_config(self):
        """保存配置"""
        try:
            config_manager.set("headless", self.headless_var.get())
            config_manager.set("use_local_chrome", self.use_local_chrome_var.get())
            config_manager.set("chrome_path", self.chrome_path_var.get())

            # 保存导出路径配置
            config_manager.set_custom_export_path(
                self.export_path_var.get(),
                self.use_custom_export_var.get()
            )

            # 保存功能相关配置
            function = self.function_var.get()
            if function == "search" and hasattr(self, 'limit_var'):
                config_manager.set("search_limit", int(self.limit_var.get()))
                config_manager.set("sort_type", self.sort_var.get())
                config_manager.set("note_type", self.note_type_var.get())
            elif function == "user_works" and hasattr(self, 'works_limit_var'):
                config_manager.set("search_limit", int(self.works_limit_var.get()))

            # 保存最后输入的内容
            if function == "search" and hasattr(self, 'keywords_var'):
                config_manager.update_last_inputs(keywords=self.keywords_var.get())
            elif function in ["user", "user_works"]:
                if hasattr(self, 'user_ids_var'):
                    config_manager.update_last_inputs(user_ids=self.user_ids_var.get())
                elif hasattr(self, 'user_works_ids_var'):
                    config_manager.update_last_inputs(user_ids=self.user_works_ids_var.get())

            config_manager.save_config()
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def open_export_dir(self):
        """打开导出目录"""
        export_dir = config_manager.get_export_dir()
        if os.path.exists(export_dir):
            os.startfile(export_dir)
        else:
            messagebox.showwarning("警告", f"导出目录不存在: {export_dir}")

    def start_collection(self):
        """开始数据采集"""
        if self.is_running:
            return

        # 验证输入
        function = self.function_var.get()
        if function == "search":
            if not hasattr(self, 'keywords_var') or not self.keywords_var.get().strip():
                messagebox.showerror("错误", "请输入搜索关键词")
                return
            # 验证搜索数量限制
            try:
                limit = int(self.limit_var.get())
                if limit <= 0:
                    raise ValueError()
            except (ValueError, AttributeError):
                messagebox.showerror("错误", "搜索数量限制必须是正整数")
                return
        elif function == "user":
            if not hasattr(self, 'user_ids_var') or not self.user_ids_var.get().strip():
                messagebox.showerror("错误", "请输入用户ID")
                return
        elif function == "user_works":
            if not hasattr(self, 'user_works_ids_var') or not self.user_works_ids_var.get().strip():
                messagebox.showerror("错误", "请输入用户ID")
                return
            # 验证作品数量限制
            try:
                limit = int(self.works_limit_var.get())
                if limit <= 0:
                    raise ValueError()
            except (ValueError, AttributeError):
                messagebox.showerror("错误", "作品数量限制必须是正整数")
                return
        elif function == "detail":
            if not hasattr(self, 'note_ids_var') or not self.note_ids_var.get().strip():
                messagebox.showerror("错误", "请输入笔记ID")
                return

        # 开始采集
        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress.start()

        # 在新线程中运行采集任务
        self.current_task = threading.Thread(target=self.run_collection_task)
        self.current_task.daemon = True
        self.current_task.start()

    def stop_collection(self):
        """停止数据采集"""
        self.is_running = False
        self.update_status("正在停止...")
        self.log_message("正在停止采集...")

    def run_collection_task(self):
        """运行采集任务（在新线程中）"""
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 运行异步任务
            loop.run_until_complete(self.async_collection_task())

        except Exception as e:
            error_msg = f"采集过程中出现错误: {e}"
            self.log_message(error_msg)
            # 使用线程安全的方式显示错误消息
            self.root.after(0, lambda msg=error_msg: self.show_error_message(msg))
        finally:
            # 重置UI状态
            self.root.after(0, self.reset_ui_state)

    async def async_collection_task(self):
        """异步采集任务"""
        try:
            self.log_message("开始初始化...")
            self.root.after(0, lambda: self.update_status("初始化中..."))

            # 创建搜索器
            try:
                search_config = config_manager.get_search_config()
                self.log_message(f"搜索配置: {search_config}")

                self.searcher = XiaoHongShuSearch(
                    headless=search_config["headless"],
                    use_local_chrome=search_config["use_local_chrome"],
                    chrome_path=search_config["chrome_path"],
                    user_data_dir=search_config["user_data_dir"]
                )
                self.log_message("搜索器创建成功")
            except Exception as e:
                self.log_message(f"创建搜索器失败: {e}")
                raise

            # 初始化
            try:
                self.log_message("开始初始化搜索器...")
                await self.searcher.initialize()
                self.log_message("初始化完成，等待登录...")
                self.root.after(0, lambda: self.update_status("等待登录..."))
            except Exception as e:
                self.log_message(f"初始化搜索器失败: {e}")
                raise

            # 根据功能执行不同的任务
            function = self.function_var.get()
            self.log_message(f"执行功能: {function}")

            if function == "search":
                await self.execute_search_task()
            elif function == "user":
                await self.execute_user_task()
            elif function == "detail":
                await self.execute_detail_task()

        except Exception as e:
            error_msg = f"任务执行失败: {e}"
            self.log_message(error_msg)
            self.root.after(0, lambda msg=error_msg: self.show_error_message(msg))
        finally:
            if self.searcher:
                await self.searcher.close()

    async def execute_search_task(self):
        """执行搜索任务"""
        keywords = self.keywords_var.get().strip()
        limit = int(self.limit_var.get())

        # 解析排序类型
        sort_type_map = {
            "latest": SearchSortType.LATEST,
            "popularity": SearchSortType.MOST_POPULAR,
            "general": SearchSortType.GENERAL
        }
        sort_type = sort_type_map.get(self.sort_var.get(), SearchSortType.LATEST)

        # 解析笔记类型
        note_type_map = {
            "all": SearchNoteType.ALL,
            "video": SearchNoteType.VIDEO,
            "image": SearchNoteType.IMAGE
        }
        note_type = note_type_map.get(self.note_type_var.get(), SearchNoteType.ALL)

        # 分割关键词
        keyword_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]

        for keyword in keyword_list:
            if not self.is_running:
                break

            self.log_message(f"开始搜索关键词: {keyword}")
            self.root.after(0, lambda k=keyword: self.update_status(f"搜索中: {k}"))

            try:
                results = await self.searcher.search(
                    keyword=keyword,
                    limit=limit,
                    sort_type=sort_type,
                    note_type=note_type
                )

                if results:
                    self.log_message(f"搜索到 {len(results)} 条结果")

                    # 导出结果
                    self.log_message("正在导出数据...")
                    filepath = self.exporter.export_search_results(results, keyword)
                    self.log_message(f"数据已导出到: {filepath}")
                else:
                    self.log_message(f"关键词 '{keyword}' 没有搜索到结果")

            except Exception as e:
                self.log_message(f"搜索关键词 '{keyword}' 时出错: {e}")

        self.log_message("搜索任务完成")
        self.root.after(0, lambda: self.update_status("搜索完成"))

    async def execute_user_task(self):
        """执行用户数据获取任务"""
        user_ids = self.user_ids_var.get().strip()
        user_id_list = [uid.strip() for uid in user_ids.split(',') if uid.strip()]

        for user_id in user_id_list:
            if not self.is_running:
                break

            self.log_message(f"开始获取用户数据: {user_id}")
            self.root.after(0, lambda u=user_id: self.update_status(f"获取用户数据: {u}"))

            try:
                user_data = await self.searcher.query_user_profile(user_id)

                if user_data:
                    self.log_message(f"成功获取用户 {user_id} 的数据")

                    # 导出结果
                    self.log_message("正在导出数据...")
                    filepath = self.exporter.export_user_data(user_data, user_id)
                    self.log_message(f"数据已导出到: {filepath}")
                else:
                    self.log_message(f"用户 '{user_id}' 没有获取到数据")

            except Exception as e:
                self.log_message(f"获取用户 '{user_id}' 数据时出错: {e}")

        self.log_message("用户数据获取任务完成")
        self.root.after(0, lambda: self.update_status("用户数据获取完成"))

    async def execute_detail_task(self):
        """执行笔记详情获取任务"""
        note_ids = self.note_ids_var.get().strip()
        note_id_list = [nid.strip() for nid in note_ids.split(',') if nid.strip()]

        note_details = []
        for note_id in note_id_list:
            if not self.is_running:
                break

            self.log_message(f"开始获取笔记详情: {note_id}")
            self.root.after(0, lambda n=note_id: self.update_status(f"获取笔记详情: {n}"))

            try:
                # 注意：这里需要xsec_source和xsec_token，简化处理
                detail = await self.searcher.get_note_detail(note_id, "", "")

                if detail:
                    note_details.append(detail)
                    self.log_message(f"成功获取笔记 {note_id} 的详情")
                else:
                    self.log_message(f"笔记 '{note_id}' 没有获取到详情")

            except Exception as e:
                self.log_message(f"获取笔记 '{note_id}' 详情时出错: {e}")

        if note_details:
            # 导出结果
            self.log_message("正在导出数据...")
            filepath = self.exporter.export_note_details(note_details, "笔记详情")
            self.log_message(f"数据已导出到: {filepath}")

        self.log_message("笔记详情获取任务完成")
        self.root.after(0, lambda: self.update_status("笔记详情获取完成"))

    def reset_ui_state(self):
        """重置UI状态"""
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress.stop()
        self.update_status("就绪")

    def run(self):
        """运行GUI应用"""
        self.root.mainloop()


def main():
    """主函数"""
    app = XHSSpiderGUI()
    app.run()


if __name__ == "__main__":
    main()
