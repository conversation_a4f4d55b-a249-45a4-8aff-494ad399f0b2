# -*- coding: utf-8 -*-
"""
创建简单的图标文件
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    """创建一个简单的图标"""
    try:
        # 创建一个64x64的图像
        size = 64
        img = Image.new('RGBA', (size, size), (255, 255, 255, 0))
        draw = ImageDraw.Draw(img)
        
        # 绘制一个红色圆形背景
        draw.ellipse([4, 4, size-4, size-4], fill=(255, 99, 71, 255), outline=(255, 69, 0, 255), width=2)
        
        # 绘制文字 "XHS"
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            font = ImageFont.load_default()
        
        text = "XHS"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2
        
        draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
        
        # 保存为ICO文件
        img.save('icon.ico', format='ICO', sizes=[(64, 64), (32, 32), (16, 16)])
        print("图标文件已创建: icon.ico")
        return True
        
    except ImportError:
        print("PIL库未安装，跳过图标创建")
        return False
    except Exception as e:
        print(f"创建图标时出错: {e}")
        return False

if __name__ == "__main__":
    create_icon()
