# 小红书数据采集工具

基于现有的小红书爬虫代码开发的GUI工具，支持关键词搜索、用户数据获取和笔记详情采集，并可导出为Excel格式。

## 功能特性

- 🔍 **关键词搜索** - 根据关键词搜索小红书笔记，支持多种排序方式
- 👤 **用户数据获取** - 获取指定用户的基本信息和笔记列表
- 🎨 **用户作品采集** - 专门获取用户发布的所有作品，支持数量限制
- 📝 **笔记详情采集** - 获取指定笔记的详细信息
- 📊 **Excel导出** - 自动将采集结果导出为Excel文件
- 📁 **自定义导出路径** - 支持选择自定义的文件保存位置
- 🖥️ **图形界面** - 简洁易用的GUI界面，支持动态参数切换
- 🔧 **配置管理** - 支持保存用户配置和浏览器状态
- 📦 **一键打包** - 可打包成独立的exe文件

## 快速开始

### 方法一：直接运行（推荐）

1. 双击运行 `install_and_run.bat`
2. 脚本会自动安装依赖并启动程序
3. 在GUI界面中配置Chrome路径
4. 选择功能并开始采集

### 方法二：手动安装

1. 确保已安装Python 3.8+
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 安装Playwright浏览器：
   ```bash
   playwright install chromium
   ```
4. 运行程序：
   ```bash
   python run.py
   ```

## 打包为exe

### 方法一：一键打包

双击运行 `build.bat`，脚本会自动完成打包过程。

### 方法二：手动打包

```bash
python build_exe.py
```

打包完成后，exe文件位于 `release` 目录中。

## 使用说明

### 1. 基本配置

- **Chrome路径**：设置本地Chrome浏览器的路径
- **无头模式**：是否在后台运行（建议首次使用时关闭）
- **用户数据目录**：自动保存在项目同级目录

### 2. 功能使用

#### 搜索关键词
- 输入关键词（多个用逗号分隔）
- 设置数量限制和排序方式
- 点击开始采集

#### 获取用户数据
- 输入用户ID（可从用户主页URL获取）
- 支持批量获取多个用户数据

#### 获取用户作品
- 输入用户ID（可从用户主页URL获取）
- 专门获取用户发布的所有作品
- 支持设置作品数量限制

#### 获取笔记详情
- 输入笔记ID（可从笔记URL获取）
- 获取详细的笔记信息

### 3. 数据导出

- 采集完成后自动导出为Excel文件
- 支持自定义导出路径或使用默认路径
- 默认保存在 `xhs_spider_data/exports` 目录
- 支持多种数据格式的结构化导出

### 4. 导出路径设置

- 勾选"使用自定义导出路径"可选择保存位置
- 点击"浏览"按钮选择目录
- 取消勾选则使用默认路径

## 注意事项

1. **登录要求**：首次使用需要在浏览器中登录小红书账号
2. **采集频率**：程序内置合理的请求间隔，避免被封
3. **网络环境**：确保能正常访问小红书网站
4. **Chrome版本**：建议使用最新版本的Chrome浏览器

## 技术栈

- **GUI框架**：tkinter
- **异步处理**：asyncio
- **浏览器自动化**：playwright
- **数据处理**：pandas
- **Excel导出**：openpyxl
- **打包工具**：pyinstaller

## 常见问题

### Q: 程序启动失败？
A: 检查Python版本（需要3.8+）和依赖安装情况

### Q: 无法登录小红书？
A: 确保网络正常，尝试手动在浏览器中完成验证

### Q: 搜索结果为空？
A: 检查关键词是否正确，某些敏感词可能被限制

### Q: 打包后的exe无法运行？
A: 确保目标机器有必要的运行时环境

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站服务条款。