# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证GUI能否正常启动
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def quick_test():
    """快速测试GUI启动"""
    print("开始快速测试...")
    
    try:
        # 导入主GUI模块
        from main_gui import XHSSpiderGUI
        print("✓ 主GUI模块导入成功")
        
        # 创建GUI实例
        app = XHSSpiderGUI()
        print("✓ GUI实例创建成功")
        
        # 显示窗口5秒后自动关闭
        app.root.after(5000, app.root.destroy)
        
        print("✓ GUI窗口将显示5秒后自动关闭")
        print("如果看到GUI窗口，说明修复成功！")
        
        # 运行GUI
        app.run()
        
        print("✓ GUI测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 快速测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("小红书数据采集工具 - 快速测试")
    print("=" * 50)
    
    if quick_test():
        print("\n✓ 快速测试通过！程序应该可以正常使用。")
    else:
        print("\n✗ 快速测试失败！请检查错误信息。")
    
    print("=" * 50)
